import { useState } from "react";
import { useNavigate } from "react-router-dom";
import MaskedInput from "react-text-mask";

import { api } from "../../../service/api";

import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

export function NewClient() {
	const [isCpfSelected, setIsCpfSelected] = useState(true);
	const [clientName, setClientName] = useState("");
	const [clientAddress, setClientAddress] = useState("");
	const [clientIdentification, setClientIdentification] = useState("");
	const [clientContact, setClientContact] = useState("");
	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const navigate = useNavigate();

	const cpfMask = [
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];

	const cnpjMask = [
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		".",
		/\d/,
		/\d/,
		/\d/,
		"/",
		/\d/,
		/\d/,
		/\d/,
		/\d/,
		"-",
		/\d/,
		/\d/,
	];

	async function handleNewClient(event: { preventDefault: () => void }) {
		event.preventDefault();

		if (!clientName) {
			setAlertMessage("O nome do cliente está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!clientAddress) {
			setAlertMessage("O endereço do cliente está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		const cpfLength = 11;
		const cnpjLength = 14;
		const cleanIdentification = clientIdentification.replace(/\D/g, "");

		if (!clientIdentification) {
			setAlertMessage(
				isCpfSelected
					? "O CPF do cliente está faltando..."
					: "O CNPJ do cliente está faltando...",
			);
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (isCpfSelected && cleanIdentification.length !== cpfLength) {
			setAlertMessage("O CPF deve conter exatamente 11 números.");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!isCpfSelected && cleanIdentification.length !== cnpjLength) {
			setAlertMessage("O CNPJ deve conter exatamente 14 números.");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!clientContact) {
			setAlertMessage("O contato do cliente está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			await api.post("/customers", {
				fullname: clientName,
				address: clientAddress,
				cpf: isCpfSelected ? cleanIdentification : null,
				cnpj: !isCpfSelected ? cleanIdentification : null,
				personType: isCpfSelected ? "individual" : "legal",
				contact: clientContact,
			});

			setAlertMessage("Cliente cadastrado com sucesso!");
			setAlertType("success");
			setAlertCallback(() => () => navigate("/lista-de-clientes"));
			setIsAlertModalOpen(true);
		} catch (error: any) {
			if (error instanceof Error) {
				setAlertMessage(
					error.response?.data?.error || "Erro ao cadastrar cliente.",
				);
			} else {
				setAlertMessage("Erro desconhecido.");
			}
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	return (
		<>
			<div className="min-h-screen flex flex-col">
				<Header pagename={"Novo Cliente"} href={"/"} $logout={false} />

				<main className="flex-grow flex flex-col items-center py-8 px-3">
					<h1 className="text-2xl font-bold text-center mb-8">
						Cadastre um novo Cliente
					</h1>

					<form className="w-full max-w-xl">
						<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4">
							{/* Nome */}
							<div className="flex flex-col">
								<label htmlFor="client-name" className="mb-1">
									Nome do Cliente:
								</label>
								<input
									type="text"
									id="client-name"
									className="p-3 border border-gray-300 rounded-md px-2 py-1"
									placeholder="Ex: Marcos Junior"
									value={clientName}
									onChange={(e) => setClientName(e.target.value)}
								/>
							</div>

							<div className="flex flex-col">
								<label htmlFor="adress" className="mb-1">
									Endereço:
								</label>
								<input
									type="text"
									id="adress"
									className="p-3 border border-gray-300 rounded-md px-2 py-1"
									placeholder="Ex: Av. Das Flores, 123"
									value={clientAddress}
									onChange={(e) => setClientAddress(e.target.value)}
								/>
							</div>

							{/* CPF / CNPJ */}
							<div className="flex flex-col">
								{/* Seleção entre CPF e CNPJ */}
								<div className="input-wrapper flex items-center space-x-4 mb-1.5">
									<div className="check flex items-center gap-1">
										<input
											type="radio"
											name="identification"
											id="cpfcheck"
											checked={isCpfSelected}
											onChange={() => setIsCpfSelected(true)}
											className="form-radio w-4 h-4"
										/>
										<label htmlFor="cpfcheck">CPF:</label>
									</div>
									<div className="check flex items-center gap-1">
										<input
											type="radio"
											name="identification"
											id="cnpjcheck"
											checked={!isCpfSelected}
											onChange={() => setIsCpfSelected(false)}
											className="form-radio w-5 h-5"
										/>
										<label htmlFor="cnpjcheck">CNPJ:</label>
									</div>
								</div>

								{/* Campo dinâmico de CPF ou CNPJ */}
								{isCpfSelected ? (
									<div className="input-wrapper">
										<label htmlFor="cpfnum" className="block mb-1.5">
											CPF:
										</label>
										<MaskedInput
											mask={cpfMask}
											id="cpfnum"
											placeholder="___.___.___-__"
											className="w-full p-3 border border-gray-300 rounded-md px-2 py-1"
											value={clientIdentification}
											onChange={(e) => setClientIdentification(e.target.value)}
										/>
									</div>
								) : (
									<div className="input-wrapper">
										<label htmlFor="cnpjnum" className="block mb-1.5">
											CNPJ:
										</label>
										<MaskedInput
											mask={cnpjMask}
											id="cnpjnum"
											placeholder="__.___.___/____-__"
											className="w-full p-3 border border-gray-300 rounded-md px-2 py-1"
											value={clientIdentification}
											onChange={(e) => setClientIdentification(e.target.value)}
										/>
									</div>
								)}
							</div>

							{/* Contato */}
							<div className="flex flex-col">
								<label htmlFor="contactinfo" className="mb-2">
									Contato:
								</label>
								<input
									type="text"
									id="contactinfo"
									className="p-3 border border-gray-300 rounded-md px-2 py-1"
									placeholder="(XX) XXXX-XXXX / <EMAIL>"
									value={clientContact}
									onChange={(e) => setClientContact(e.target.value)}
								/>
							</div>

							<div className="flex w-full justify-center">
								<button
									type="button"
									className="font-semibold bg-blue-300 border border-gray-300 rounded px-6 py-2 mt-2"
									onClick={handleNewClient}
								>
									Confirmar adição
								</button>
							</div>
						</fieldset>
					</form>
				</main>

				<Footer />
			</div>

			{isAlertModalOpen && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-roboto">
					<div className="bg-white p-8 rounded border border-gray-300 max-w-md w-full mx-4">
						<h3 className="text-xl font-inter mb-4 text-center">
							{alertMessage}
						</h3>

						<div className="flex justify-center gap-4">
							{alertType === "confirm" ? (
								<>
									<button
										type="button"
										className="border border-gray-300 rounded bg-gray-200 py-2 px-6 hover:bg-gray-300 transition-colors"
										onClick={() => setIsAlertModalOpen(false)}
									>
										Cancelar
									</button>
									{alertCallback && (
										<button
											type="button"
											className="border border-gray-300 rounded bg-red-200 py-2 px-6 hover:bg-red-300 transition-colors"
											onClick={() => alertCallback()}
										>
											Confirmar
										</button>
									)}
								</>
							) : (
								<button
									type="button"
									className={`border border-gray-300 rounded py-2 px-6 transition-colors ${alertType === "success"
											? "bg-green-200 hover:bg-green-300"
											: "bg-red-200 hover:bg-red-300"
										}`}
									onClick={() => {
										setIsAlertModalOpen(false);
										if (alertType === "success" && alertCallback) {
											alertCallback();
										}
									}}
								>
									OK
								</button>
							)}
						</div>
					</div>
				</div>
			)}
		</>
	);
}
