import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { api } from "../../service/api";
import { AlertModal } from "../AlertModal";

interface CadModeloProps {
	$btnactivated: boolean;
	$return: boolean;
	$hidden: boolean;
	onSubmit: () => void;
}

interface Fio {
	tipo: string;
	quantidade: number;
}

export function CadModelo({
	$btnactivated,
	$return,
	$hidden,
	onSubmit,
}: CadModeloProps) {
	const [modelName, setModelName] = useState("");
	const [modelCode, setModelCode] = useState("");
	const [modelPrice, setModelPrice] = useState("");
	const [modelColor, setModelColor] = useState("");
	const [modelSize, setModelSize] = useState("");
	const [selectedFio, setSelectedFio] = useState("");
	const [quantidade, setQuantidade] = useState("");
	const [fios, setFios] = useState<Fio[]>([]);
	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const navigate = useNavigate();

	// Simular dados do backend - Substituir por chamada API real
	const tiposFio = [
		{ id: "1", nome: "Fio 100% Algodão" },
		{ id: "2", nome: "Fio Amigurumi" },
		{ id: "3", nome: "Fio Mesclado" },
	];

	const handleAddFio = () => {
		if (selectedFio && quantidade) {
			const novoFio: Fio = {
				tipo: selectedFio,
				quantidade: Number(quantidade),
			};
			setFios([...fios, novoFio]);
			setSelectedFio("");
			setQuantidade("");
		}
	};

	const handleRemoveFio = (index: number) => {
		setFios(fios.filter((_, i) => i !== index));
	};

	async function handleNewModel() {
		if (!modelName) {
			setAlertMessage("O nome do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelCode) {
			setAlertMessage("O código do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelPrice) {
			setAlertMessage("O preço do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelColor) {
			setAlertMessage("A cor do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!modelSize) {
			setAlertMessage("O tamanho do modelo está faltando...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			await api.post("/garments", {
				name: modelName,
				refcode: modelCode.toUpperCase(),
				price: Number.parseFloat(modelPrice.toString()),
				size: modelSize,
				color: modelColor,
			});

			setAlertMessage("Modelo cadastrado com sucesso!");
			setAlertType("success");
			setIsAlertModalOpen(true);

			// Limpa os campos após sucesso
			setModelName("");
			setModelCode("");
			setModelPrice("");
			setModelSize("");
			setModelColor("");

			// Chama o callback de atualização se existir
			if (onSubmit) {
				onSubmit();
			}

			if ($return) {
				navigate(-1);
			}
		} catch (error) {
			setAlertMessage("Erro ao cadastrar modelo.");
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	if ($hidden) return null;

	return (
		<>
			<div className="w-full max-w-4xl">
				<form
					id="cad-modelo"
					className="bg-white shadow-md rounded mb-4"
					onSubmit={(e) => {
						e.preventDefault();
						handleNewModel();
					}}
				>
					<fieldset className="p-4 border border-gray-300 rounded-md">
						<div className="flex flex-col gap-4 items-center sm:items-start mb-6">
							{$btnactivated && (
								<button
									type="button"
									className="bg-blue-300 border border-gray-300 rounded px-4 py-2"
								>
									Adicionar Modelo
								</button>
							)}

							<div className="input-wrapper flex flex-col w-64 sm:w-full">
								<label htmlFor="nwmodel-name" className="mb-1">
									Nome do novo Modelo:
								</label>
								<input
									type="text"
									id="nwmodel-name"
									className="text-sm border border-gray-300 rounded px-2 py-1"
									placeholder="Ex: Blusa Lacinho"
									value={modelName}
									onChange={(e) => setModelName(e.target.value)}
								/>
							</div>

							<div className="line-wrapper flex flex-col sm:flex-row justify-between gap-4 w-64 sm:w-full">
								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="model-code" className="mb-1 truncate">
										Código do modelo:
									</label>
									<input
										type="text"
										id="model-code"
										placeholder="Apenas código"
										className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
										value={modelCode}
										onChange={(e) => setModelCode(e.target.value)}
									/>
								</div>

								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="model-price" className="mb-1">
										Preço:
									</label>
									<input
										type="number"
										min={1}
										step="0.01"
										placeholder="R$ 0,00"
										id="model-price"
										className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
										value={modelPrice}
										onChange={(e) => setModelPrice(e.target.value)}
									/>
								</div>
							</div>

							<div className="line-wrapper flex flex-col sm:flex-row justify-between gap-4 w-64 sm:w-full">
								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="model-color" className="mb-1">
										Cor:
									</label>
									<input
										type="text"
										id="model-color"
										className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
										placeholder="Ex: Azul Claro"
										value={modelColor}
										onChange={(e) => setModelColor(e.target.value)}
									/>
								</div>

								<div className="input-wrapper flex flex-col flex-grow">
									<label htmlFor="model-size" className="mb-1">
										Tamanho:
									</label>
									<input
										type="text"
										id="model-size"
										className="text-sm border border-gray-300 rounded px-2 py-1 w-full"
										placeholder="Ex: M"
										value={modelSize}
										onChange={(e) => setModelSize(e.target.value)}
									/>
								</div>
							</div>
						</div>

						<div className="mb-6">
							<h2 className="text-xl font-semibold mb-4">
								Materiais Necessários
							</h2>

							<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
								<div>
									<label
										htmlFor="fio-type"
										className="block text-sm font-bold mb-2"
									>
										Tipo de Fio
									</label>
									<select
										id="fio-type"
										value={selectedFio}
										onChange={(e) => setSelectedFio(e.target.value)}
										className="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
									>
										<option value="">Selecione um fio</option>
										{tiposFio.map((fio) => (
											<option key={fio.id} value={fio.nome}>
												{fio.nome}
											</option>
										))}
									</select>
								</div>

								<div>
									<label
										htmlFor="fio-quantity"
										className="block text-sm font-bold mb-2"
									>
										Quantidade (g)
									</label>
									<input
										type="number"
										id="fio-quantity"
										value={quantidade}
										onChange={(e) => setQuantidade(e.target.value)}
										className="appearance-none border rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline"
										placeholder="Digite a quantidade"
									/>
								</div>

								<div className="flex items-end">
									<button
										type="button"
										onClick={handleAddFio}
										className="bg-blue-300 hover:bg-blue-400 text-black font-medium py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
									>
										Adicionar Fio
									</button>
								</div>
							</div>

							{fios.length > 0 && (
								<div className="mt-4">
									<h3 className="text-lg font-medium mb-2">
										Fios Adicionados:
									</h3>
									<ul className="space-y-2">
										{fios.map((fio, index) => (
											<li
												key={index}
												className="flex justify-between items-center bg-gray-50 p-3 rounded-md"
											>
												<span>
													{fio.tipo} - {fio.quantidade}g
												</span>
												<button
													type="button"
													onClick={() => handleRemoveFio(index)}
													className="text-red-500 hover:text-red-700"
												>
													Remover
												</button>
											</li>
										))}
									</ul>
								</div>
							)}
						</div>

						<div className="flex justify-center w-full">
							<button
								type="submit"
								className="font-semibold bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
							>
								Confirmar adição
							</button>
						</div>
					</fieldset>
				</form>
			</div>
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => setIsAlertModalOpen(false)}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</>
	);
}
